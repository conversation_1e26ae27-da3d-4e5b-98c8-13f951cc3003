"""
Speech provider factory for creating STT and TTS provider instances.

This module implements the factory pattern to create speech provider instances
based on configuration, making it easy to switch between different providers.
"""

from typing import Dict, Any, Type
from enum import Enum

from .base import BaseTTSProvider, BaseSTTProvider
from .providers.elevenlabs import ElevenLabsProvider, ElevenLabsTTSProvider, ElevenLabsSTTProvider
from .providers.openai import OpenAIProvider, OpenAITTSProvider, OpenAISTTProvider


class SpeechProviderType(str, Enum):
    """Enumeration of available speech providers."""
    ELEVENLABS = "elevenlabs"
    OPENAI = "openai"
    # Future providers can be added here
    # GOOGLE = "google"
    # AZURE = "azure"


class SpeechProviderFactory:
    """Factory class for creating speech provider instances."""
    
    # Registry of available providers
    _tts_providers: Dict[SpeechProviderType, Type[BaseTTSProvider]] = {
        SpeechProviderType.ELEVENLABS: ElevenLabsTTSProvider,
        SpeechProviderType.OPENAI: OpenAITTSProvider,
    }
    
    _stt_providers: Dict[SpeechProviderType, Type[BaseSTTProvider]] = {
        SpeechProviderType.ELEVENLABS: ElevenLabsSTTProvider,
        SpeechProviderType.OPENAI: OpenAISTTProvider,
    }
    
    _combined_providers: Dict[SpeechProviderType, Type] = {
        SpeechProviderType.ELEVENLABS: ElevenLabsProvider,
        SpeechProviderType.OPENAI: OpenAIProvider,
    }
    
    @classmethod
    def create_tts_provider(
        cls, 
        provider_type: SpeechProviderType, 
        api_key: str, 
        **kwargs
    ) -> BaseTTSProvider:
        """
        Create a TTS provider instance.
        
        Args:
            provider_type: Type of provider to create
            api_key: API key for the provider
            **kwargs: Additional provider-specific configuration
            
        Returns:
            TTS provider instance
            
        Raises:
            ValueError: If provider type is not supported
        """
        if provider_type not in cls._tts_providers:
            raise ValueError(f"Unsupported TTS provider: {provider_type}")
        
        provider_class = cls._tts_providers[provider_type]
        return provider_class(api_key=api_key, **kwargs)
    
    @classmethod
    def create_stt_provider(
        cls, 
        provider_type: SpeechProviderType, 
        api_key: str, 
        **kwargs
    ) -> BaseSTTProvider:
        """
        Create an STT provider instance.
        
        Args:
            provider_type: Type of provider to create
            api_key: API key for the provider
            **kwargs: Additional provider-specific configuration
            
        Returns:
            STT provider instance
            
        Raises:
            ValueError: If provider type is not supported
        """
        if provider_type not in cls._stt_providers:
            raise ValueError(f"Unsupported STT provider: {provider_type}")
        
        provider_class = cls._stt_providers[provider_type]
        return provider_class(api_key=api_key, **kwargs)
    
    @classmethod
    def create_combined_provider(
        cls, 
        provider_type: SpeechProviderType, 
        api_key: str, 
        **kwargs
    ):
        """
        Create a combined provider instance (both TTS and STT).
        
        Args:
            provider_type: Type of provider to create
            api_key: API key for the provider
            **kwargs: Additional provider-specific configuration
            
        Returns:
            Combined provider instance
            
        Raises:
            ValueError: If provider type is not supported
        """
        if provider_type not in cls._combined_providers:
            raise ValueError(f"Unsupported combined provider: {provider_type}")
        
        provider_class = cls._combined_providers[provider_type]
        return provider_class(api_key=api_key, **kwargs)
    
    @classmethod
    def get_available_providers(cls) -> Dict[str, list]:
        """
        Get list of available providers.
        
        Returns:
            Dictionary with lists of available TTS and STT providers
        """
        return {
            "tts_providers": list(cls._tts_providers.keys()),
            "stt_providers": list(cls._stt_providers.keys()),
            "combined_providers": list(cls._combined_providers.keys())
        }
    
    @classmethod
    def register_tts_provider(
        cls, 
        provider_type: SpeechProviderType, 
        provider_class: Type[BaseTTSProvider]
    ):
        """
        Register a new TTS provider.
        
        Args:
            provider_type: Type identifier for the provider
            provider_class: Provider class that implements BaseTTSProvider
        """
        cls._tts_providers[provider_type] = provider_class
    
    @classmethod
    def register_stt_provider(
        cls, 
        provider_type: SpeechProviderType, 
        provider_class: Type[BaseSTTProvider]
    ):
        """
        Register a new STT provider.
        
        Args:
            provider_type: Type identifier for the provider
            provider_class: Provider class that implements BaseSTTProvider
        """
        cls._stt_providers[provider_type] = provider_class
    
    @classmethod
    def register_combined_provider(
        cls, 
        provider_type: SpeechProviderType, 
        provider_class: Type
    ):
        """
        Register a new combined provider.
        
        Args:
            provider_type: Type identifier for the provider
            provider_class: Provider class that provides both TTS and STT
        """
        cls._combined_providers[provider_type] = provider_class