2025-08-18 13:33:30,443 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250818_133330.log
2025-08-18 13:33:30,443 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-18 13:33:30,443 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: f5379ba9-4467-43dd-a630-a08a332fe6b7
2025-08-18 13:33:30,443 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-18 13:33:30,443 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institution has the most students?'
2025-08-18 13:33:30,443 - REPORT_REQUEST - INFO - 🆔 Task ID: f5379ba9-4467-43dd-a630-a08a332fe6b7
2025-08-18 13:33:30,443 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-18T13:33:30.443800
2025-08-18 13:33:30,579 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-18 13:33:30,580 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 180
2025-08-18 13:33:30,711 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-18 13:33:30,711 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 97
2025-08-18 13:33:30,848 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.137s]
2025-08-18 13:33:30,849 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-18 13:33:30,849 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (30 docs)
2025-08-18 13:33:30,849 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (25 docs)
2025-08-18 13:33:30,849 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (22 docs)
2025-08-18 13:33:30,849 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-18 13:33:30,850 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-18 13:33:30,850 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-18 13:33:30,850 - ELASTICSEARCH_STATE - INFO -   - 'From what institution do student owe the most fees?' (9 docs)
2025-08-18 13:33:30,850 - ELASTICSEARCH_STATE - INFO -   - 'What is the fee owing situtation at ITC University?' (9 docs)
2025-08-18 13:33:30,850 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-18 13:33:30,850 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-18 13:33:30,851 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-18 13:33:30,851 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-18 13:33:30,851 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-18 13:33:30,851 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Which institution has the most students?
2025-08-18 13:33:30,851 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-18 13:33:30,851 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
