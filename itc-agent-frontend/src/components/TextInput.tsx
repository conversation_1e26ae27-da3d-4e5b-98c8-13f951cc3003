import { GlobeIcon, MicIcon, PlusIcon, SendIcon } from 'lucide-react'
import React, { useState, KeyboardEvent } from 'react'
import { AudioRecorder } from './AudioRecorder'

interface Props {
    onSubmit?: (message: string) => void;
    placeholder?: string;
    disabled?: boolean;
}

export default function TextInput({ onSubmit, placeholder = "Enter message", disabled = false }: Props) {
    const [value, setValue] = useState('');
    const [showAudioRecorder, setShowAudioRecorder] = useState(false);

    const handleSubmit = () => {
        if (value.trim() && onSubmit && !disabled) {
            onSubmit(value.trim());
            setValue('');
        }
    };

    const handleTranscription = (text: string) => {
        setValue(text);
        setShowAudioRecorder(false);
    };

    const handleAudioError = (error: string) => {
        console.error('Audio error:', error);
        // You could show a toast notification here
    };

    const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit();
        }
    };

    return (
        <div className='flex flex-col w-full max-w-[50rem] mx-auto'>
            {/* Audio Recorder */}
            {showAudioRecorder && (
                <div className='mb-3 p-4 border-2 border-border bg-card rounded-lg'>
                    <div className='flex items-center justify-between mb-3'>
                        <h3 className='text-sm font-medium text-foreground'>Voice Recording</h3>
                        <button
                            onClick={() => setShowAudioRecorder(false)}
                            className='text-muted-foreground hover:text-foreground text-sm'
                        >
                            Cancel
                        </button>
                    </div>
                    <AudioRecorder
                        onTranscription={handleTranscription}
                        onError={handleAudioError}
                        disabled={disabled}
                    />
                </div>
            )}

            {/* Text Input */}
            <div className='border-2 border-border bg-card rounded-lg'>
                <input
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={placeholder}
                    disabled={disabled}
                    className='placeholder-muted-foreground text-foreground bg-transparent w-full outline-none px-4 py-3 font-medium disabled:opacity-50'
                />
                <div className='flex justify-between px-4 py-3'>
                    <div className='flex gap-4'>
                        <div className='text-muted-foreground flex justify-center items-center size-[2rem] cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-full transition-colors'>
                            <PlusIcon className='' size={"1.3rem"} />
                        </div>
                        <div className='flex cursor-pointer hover:bg-accent hover:text-accent-foreground h-[2rem] px-2 gap-2 rounded-full items-center transition-colors'>
                            <GlobeIcon className='text-muted-foreground' size={"1rem"} />
                            <div className='text-muted-foreground text-[0.92rem]'>Web Search</div>
                        </div>
                    </div>
                    <div className='flex gap-2'>
                        <button
                            onClick={() => setShowAudioRecorder(!showAudioRecorder)}
                            disabled={disabled}
                            className={`text-muted-foreground flex justify-center items-center size-[2rem] cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${showAudioRecorder ? 'bg-accent text-accent-foreground' : ''}`}
                            title="Voice input"
                        >
                            <MicIcon className='' size={"1.3rem"} />
                        </button>
                        {value.trim() && (
                            <button
                                onClick={handleSubmit}
                                disabled={disabled}
                                className='text-primary flex justify-center items-center size-[2rem] cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                            >
                                <SendIcon className='' size={"1.3rem"} />
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}
