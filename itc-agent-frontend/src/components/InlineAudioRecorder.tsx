"use client";

import React, { useState, useEffect } from 'react';
import { MicIcon, Square, Play, Pause, Trash2, Send, Loader2 } from 'lucide-react';
import { useAudioRecorder } from '@/hooks/useAudioRecorder';
import { speechApi } from '@/services/speechApi';
import { useVoicePreferences } from '@/contexts/VoicePreferencesContext';

interface InlineAudioRecorderProps {
  onTranscription?: (text: string) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  disabled?: boolean;
}

export const InlineAudioRecorder: React.FC<InlineAudioRecorderProps> = ({
  onTranscription,
  onError,
  onCancel,
  disabled = false
}) => {
  const [isTranscribing, setIsTranscribing] = useState(false);
  const { preferences } = useVoicePreferences();
  
  const {
    isRecording,
    isPaused,
    recordingTime,
    audioBlob,
    audioUrl,
    error: recordingError,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    clearRecording,
  } = useAudioRecorder();

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartRecording = async () => {
    try {
      await startRecording();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start recording';
      onError?.(errorMessage);
    }
  };

  const handleTranscribe = async () => {
    if (!audioBlob) return;

    try {
      setIsTranscribing(true);
      
      // Convert blob to file
      const audioFile = new File([audioBlob], 'recording.webm', { type: 'audio/webm' });
      
      // Send to speech-to-text API
      const response = await speechApi.speechToText(audioFile, {
        language_code: 'eng',
        tag_audio_events: true,
        diarize: false,
      });
      
      onTranscription?.(response.text);
      
      // Auto-clear recording after transcription
      clearRecording();
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Transcription failed';
      onError?.(errorMessage);
    } finally {
      setIsTranscribing(false);
    }
  };

  // Auto-transcribe and send if auto-send is enabled
  useEffect(() => {
    if (audioBlob && preferences.autoSendVoiceMessages && !isTranscribing) {
      handleTranscribe();
    }
  }, [audioBlob, preferences.autoSendVoiceMessages]);

  const error = recordingError;

  // Recording state indicator
  const getRecordingIndicator = () => {
    if (isRecording && !isPaused) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          <span className="text-sm font-mono text-red-600">
            REC {formatTime(recordingTime)}
          </span>
        </div>
      );
    }
    
    if (isPaused) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-yellow-500 rounded-full" />
          <span className="text-sm font-mono text-yellow-600">
            PAUSED {formatTime(recordingTime)}
          </span>
        </div>
      );
    }

    if (audioBlob) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          <span className="text-sm text-green-600">
            Recording ready
          </span>
        </div>
      );
    }

    return null;
  };

  // Recording controls
  const getRecordingControls = () => {
    if (!isRecording && !audioBlob) {
      // Initial state - start recording
      return (
        <button
          onClick={handleStartRecording}
          disabled={disabled}
          className="flex items-center justify-center w-8 h-8 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-full transition-colors"
          title="Start Recording"
        >
          <MicIcon size={16} />
        </button>
      );
    }

    if (isRecording) {
      // Recording state - show stop and pause/resume
      return (
        <div className="flex items-center gap-2">
          <button
            onClick={stopRecording}
            className="flex items-center justify-center w-8 h-8 bg-gray-600 hover:bg-gray-700 text-white rounded-full transition-colors"
            title="Stop Recording"
          >
            <Square size={14} />
          </button>
          
          {!isPaused ? (
            <button
              onClick={pauseRecording}
              className="flex items-center justify-center w-6 h-6 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full transition-colors"
              title="Pause Recording"
            >
              <Pause size={12} />
            </button>
          ) : (
            <button
              onClick={resumeRecording}
              className="flex items-center justify-center w-6 h-6 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors"
              title="Resume Recording"
            >
              <Play size={12} />
            </button>
          )}
        </div>
      );
    }

    if (audioBlob) {
      // Recorded state - show playback and action controls
      return (
        <div className="flex items-center gap-2">
          <audio controls src={audioUrl || undefined} className="h-6 text-xs">
            Your browser does not support the audio element.
          </audio>
          
          {!preferences.autoSendVoiceMessages && (
            <button
              onClick={handleTranscribe}
              disabled={isTranscribing}
              className="flex items-center justify-center w-6 h-6 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-full transition-colors"
              title="Transcribe to Text"
            >
              {isTranscribing ? (
                <Loader2 size={12} className="animate-spin" />
              ) : (
                <Send size={12} />
              )}
            </button>
          )}
          
          <button
            onClick={clearRecording}
            className="flex items-center justify-center w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
            title="Clear Recording"
          >
            <Trash2 size={12} />
          </button>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="flex items-center justify-between w-full">
      {/* Left side - Recording indicator */}
      <div className="flex items-center gap-2 min-w-0 flex-1">
        {getRecordingIndicator()}
        
        {/* Error display */}
        {error && (
          <span className="text-xs text-red-600 truncate">
            {error}
          </span>
        )}
        
        {/* Transcribing indicator */}
        {isTranscribing && (
          <span className="text-xs text-blue-600">
            Transcribing...
          </span>
        )}
      </div>

      {/* Right side - Controls */}
      <div className="flex items-center gap-2">
        {getRecordingControls()}
        
        {/* Cancel button */}
        <button
          onClick={onCancel}
          className="text-xs text-muted-foreground hover:text-foreground px-2 py-1 rounded transition-colors"
          title="Cancel Recording"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};
